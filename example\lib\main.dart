import 'dart:developer';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:easy_social_share/easy_social_share.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: "Easy Social Share Demo",
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      home: const ShareDemoScreen(),
    );
  }
}

class ShareDemoScreen extends StatefulWidget {
  const ShareDemoScreen({super.key});

  @override
  State<ShareDemoScreen> createState() => _ShareDemoScreenState();
}

class _ShareDemoScreenState extends State<ShareDemoScreen> {
  final EasySocialShare _easySocialShare = EasySocialShare();
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Easy Social Share'),
        // backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        centerTitle: true,
        primary: true,
        elevation: 0.5,
      ),
      body: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Share Buttons
            _buildShareButton(
              icon: Icons.system_update_alt,
              label: 'Share image to system',
              description: 'Use native Android share dialog',
              color: Colors.blue,
              onPressed: _isLoading
                  ? null
                  : () async {
                      await _handleShare(_shareToSystem);
                    },
            ),
            const SizedBox(height: 16),
            _buildShareButton(
              icon: Icons.chat,
              label: 'Share image to WhatsApp',
              description: 'Share directly to WhatsApp',
              color: Colors.green,
              onPressed: _isLoading
                  ? null
                  : () async {
                      await _handleShare(_shareToWhatsApp);
                    },
            ),

            if (_isLoading) ...[
              const SizedBox(height: 32),
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              const Text(
                'Processing...',
                style: TextStyle(color: Colors.grey),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildShareButton({
    required IconData icon,
    required String label,
    required String description,
    required Color color,
    required VoidCallback? onPressed,
  }) {
    return Container(
      width: double.infinity,
      height: 80,
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: color,
          foregroundColor: Colors.white,
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.white.withValues(alpha: 0.8),
                    ),
                  ),
                ],
              ),
            ),
            const Icon(Icons.arrow_forward_ios, size: 16),
          ],
        ),
      ),
    );
  }

  Future<void> _handleShare(
    Future<void> Function(String, String) shareFunction,
  ) async {
    setState(() {
      _isLoading = true;
    });

    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
      );

      if (result != null &&
          result.paths.isNotEmpty &&
          result.paths[0] != null) {
        await shareFunction(
            "Check out this amazing image! 📸", result.paths[0]!);
        _showSnackBar('Share completed successfully!', Colors.green);
      } else {
        _showSnackBar('No image selected', Colors.orange);
      }
    } catch (e) {
      log('Share error: $e');
      _showSnackBar('Failed to share: $e', Colors.red);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<String> _shareToSystem(String message, String filePath) async {
    final String result = await _easySocialShare.android.shareToSystem(
      'Share Image',
      message,
      filePath,
    );

    log('System share result: $result');

    return result;
  }

  Future<String> _shareToWhatsApp(String message, String filePath) async {
    final String result = await _easySocialShare.android.shareToWhatsapp(
      message,
      filePath,
    );

    log('WhatsApp share result: $result');

    return result;
  }

  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        duration: const Duration(seconds: 3),
      ),
    );
  }
}
